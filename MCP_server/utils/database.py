"""
数据库模型和操作类
"""
from sqlalchemy import create_engine, Column, String, Text, DateTime, Enum, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.dialects.mysql import CHAR
from datetime import datetime
import uuid
import enum

# 数据库配置
DATABASE_URL = "mysql+pymysql://root:immotorsAI123456@**************:3566/immotors_ai_dev"

# 创建数据库引擎
engine = create_engine(DATABASE_URL, echo=True)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基类
Base = declarative_base()

# 优先级枚举
class PriorityEnum(enum.Enum):
    P0 = "P0"
    P1 = "P1"
    P2 = "P2"

# 测试用例集表
class TestCaseSet(Base):
    __tablename__ = "test_case_sets"
    
    # 使用UUID作为主键
    uuid = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # 用例集名称
    name = Column(String(255), nullable=False, comment="测试用例集名称")
    
    # 原始PRD文档链接
    prd_doc_link = Column(Text, nullable=False, comment="原始PRD文档链接")
    
    # PRD文档标题
    prd_doc_title = Column(String(500), nullable=True, comment="PRD文档标题")
    
    # 作者
    author = Column(String(100), nullable=True, comment="作者")
    
    # 创建时间
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    
    # 更新时间
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关联的测试用例
    test_cases = relationship("TestCase", back_populates="test_case_set", cascade="all, delete-orphan")

# 测试用例表
class TestCase(Base):
    __tablename__ = "test_cases"
    
    # 使用UUID作为主键
    uuid = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # 关联的测试用例集UUID
    test_case_set_uuid = Column(CHAR(36), ForeignKey("test_case_sets.uuid"), nullable=False, comment="测试用例集UUID")
    
    # 测试用例标题
    title = Column(String(500), nullable=False, comment="测试用例标题")
    
    # 优先级
    priority = Column(Enum(PriorityEnum), nullable=False, comment="优先级(P0/P1/P2)")
    
    # 是否需要自测
    requires_self_test = Column(Boolean, nullable=False, comment="是否需要自测")
    
    # 描述信息
    description = Column(Text, nullable=True, comment="测试用例描述")
    
    # 创建时间
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    
    # 更新时间
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关联的测试用例集
    test_case_set = relationship("TestCaseSet", back_populates="test_cases")

# 数据库操作类
class DatabaseManager:
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
    
    def create_tables(self):
        """创建所有表"""
        Base.metadata.create_all(bind=self.engine)
    
    def get_session(self):
        """获取数据库会话"""
        return self.SessionLocal()
    
    def close_session(self, session):
        """关闭数据库会话"""
        session.close()

# 测试用例集存储类
class TestCaseStorage:
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def create_test_case_set(self, name: str, prd_doc_link: str, prd_doc_title: str = None, author: str = None):
        """创建测试用例集"""
        session = self.db_manager.get_session()
        try:
            test_case_set = TestCaseSet(
                name=name,
                prd_doc_link=prd_doc_link,
                prd_doc_title=prd_doc_title,
                author=author
            )
            session.add(test_case_set)
            session.commit()
            session.refresh(test_case_set)
            return test_case_set
        except Exception as e:
            session.rollback()
            raise e
        finally:
            self.db_manager.close_session(session)
    
    def get_test_case_set(self, uuid: str):
        """根据UUID获取测试用例集"""
        session = self.db_manager.get_session()
        try:
            return session.query(TestCaseSet).filter(TestCaseSet.uuid == uuid).first()
        finally:
            self.db_manager.close_session(session)
    
    def update_test_case_set(self, uuid: str, **kwargs):
        """更新测试用例集"""
        session = self.db_manager.get_session()
        try:
            test_case_set = session.query(TestCaseSet).filter(TestCaseSet.uuid == uuid).first()
            if test_case_set:
                for key, value in kwargs.items():
                    if hasattr(test_case_set, key):
                        setattr(test_case_set, key, value)
                session.commit()
                session.refresh(test_case_set)
                return test_case_set
            return None
        except Exception as e:
            session.rollback()
            raise e
        finally:
            self.db_manager.close_session(session)
    
    def delete_test_case_set(self, uuid: str):
        """删除测试用例集"""
        session = self.db_manager.get_session()
        try:
            test_case_set = session.query(TestCaseSet).filter(TestCaseSet.uuid == uuid).first()
            if test_case_set:
                session.delete(test_case_set)
                session.commit()
                return True
            return False
        except Exception as e:
            session.rollback()
            raise e
        finally:
            self.db_manager.close_session(session)
    
    def list_test_case_sets(self, limit: int = 100, offset: int = 0):
        """列出测试用例集"""
        session = self.db_manager.get_session()
        try:
            return session.query(TestCaseSet).offset(offset).limit(limit).all()
        finally:
            self.db_manager.close_session(session)

# 测试用例存储类
class TestCaseManager:
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def create_test_case(self, test_case_set_uuid: str, title: str, priority: str, description: str = None):
        """创建测试用例"""
        session = self.db_manager.get_session()
        try:
            # P0优先级必须需要自测
            requires_self_test = (priority == "P0")
            
            test_case = TestCase(
                test_case_set_uuid=test_case_set_uuid,
                title=title,
                priority=PriorityEnum(priority),
                requires_self_test=requires_self_test,
                description=description
            )
            session.add(test_case)
            session.commit()
            session.refresh(test_case)
            return test_case
        except Exception as e:
            session.rollback()
            raise e
        finally:
            self.db_manager.close_session(session)
    
    def get_test_case(self, uuid: str):
        """根据UUID获取测试用例"""
        session = self.db_manager.get_session()
        try:
            return session.query(TestCase).filter(TestCase.uuid == uuid).first()
        finally:
            self.db_manager.close_session(session)
    
    def get_test_cases_by_set(self, test_case_set_uuid: str):
        """根据测试用例集UUID获取所有测试用例"""
        session = self.db_manager.get_session()
        try:
            return session.query(TestCase).filter(TestCase.test_case_set_uuid == test_case_set_uuid).all()
        finally:
            self.db_manager.close_session(session)
    
    def update_test_case(self, uuid: str, **kwargs):
        """更新测试用例"""
        session = self.db_manager.get_session()
        try:
            test_case = session.query(TestCase).filter(TestCase.uuid == uuid).first()
            if test_case:
                for key, value in kwargs.items():
                    if hasattr(test_case, key):
                        if key == 'priority':
                            value = PriorityEnum(value)
                            # 如果优先级改为P0，自动设置需要自测
                            if value == PriorityEnum.P0:
                                test_case.requires_self_test = True
                        setattr(test_case, key, value)
                session.commit()
                session.refresh(test_case)
                return test_case
            return None
        except Exception as e:
            session.rollback()
            raise e
        finally:
            self.db_manager.close_session(session)
    
    def delete_test_case(self, uuid: str):
        """删除测试用例"""
        session = self.db_manager.get_session()
        try:
            test_case = session.query(TestCase).filter(TestCase.uuid == uuid).first()
            if test_case:
                session.delete(test_case)
                session.commit()
                return True
            return False
        except Exception as e:
            session.rollback()
            raise e
        finally:
            self.db_manager.close_session(session)

# 兼容性类（保持原有的JSONStorage接口）
class JSONStorage:
    """为了兼容现有代码而保留的类"""
    def __init__(self):
        pass
    
    def save(self, data):
        """保存数据的占位方法"""
        pass
    
    def load(self):
        """加载数据的占位方法"""
        return {}

# 初始化数据库
def init_database():
    """初始化数据库表"""
    db_manager = DatabaseManager()
    # 先删除所有表，然后重新创建
    Base.metadata.drop_all(bind=db_manager.engine)
    db_manager.create_tables()
    print("数据库表创建完成")

if __name__ == "__main__":
    init_database()
