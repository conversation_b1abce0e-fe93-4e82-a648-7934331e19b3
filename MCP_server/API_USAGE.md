# 测试用例管理API使用说明

## 概述

本服务提供了两种接口方式：
1. **MCP协议接口** - 挂载在 `/mcp` 路径下，用于MCP客户端调用
2. **RESTful API接口** - 挂载在根路径下，提供标准的HTTP API

## 服务启动

```bash
# 启动服务器（默认端口8001）
python3 fastmcp_server.py
```

服务启动后可以访问：
- **API文档**: http://localhost:8001/docs (Swagger UI)
- **ReDoc文档**: http://localhost:8001/redoc
- **健康检查**: http://localhost:8001/health

## RESTful API接口

### 基础接口

#### 健康检查
```http
GET /health
```

#### API信息
```http
GET /api/info
```

### 测试用例集管理

#### 创建测试用例集
```http
POST /api/v1/testcase-sets
Content-Type: application/json

{
  "name": "用户登录功能测试用例集",
  "prd_doc_link": "https://example.feishu.cn/docx/abc123",
  "prd_doc_title": "用户登录功能PRD文档",
  "prd_doc_content": "PRD文档的完整文本内容...",
  "prd_doc_type": "feishu_doc",
  "prd_doc_format": "markdown",
  "author": "张三"
}
```

**字段说明：**
- `name`: 测试用例集名称（必填）
- `prd_doc_link`: PRD文档链接（必填）
- `prd_doc_title`: PRD文档标题（可选）
- `prd_doc_content`: PRD文档文本内容（可选）
- `prd_doc_type`: 文档类型（可选）- `feishu_wiki`、`feishu_doc`、`local_file`
- `prd_doc_format`: 文档格式（可选）- `markdown`、`pdf`、`txt`、`docx`、`html`
- `author`: 作者（可选）

#### 获取测试用例集信息
```http
GET /api/v1/testcase-sets/{uuid}
```

#### 获取测试用例集列表
```http
GET /api/v1/testcase-sets?limit=100&offset=0
```

### 测试用例管理

#### 创建测试用例
```http
POST /api/v1/testcases
Content-Type: application/json

{
  "test_case_set_uuid": "用例集UUID",
  "title": "用户正常登录功能验证",
  "priority": "P0",
  "description": "验证用户使用正确的手机号和密码能够成功登录"
}
```

**字段说明：**
- `test_case_set_uuid`: 测试用例集UUID（必填）
- `title`: 测试用例标题（必填）
- `priority`: 优先级（必填）- `P0`、`P1`、`P2`
- `description`: 测试用例描述（可选）

#### 获取测试用例列表
```http
GET /api/v1/testcase-sets/{set_uuid}/testcases
```

#### 更新测试用例
```http
PUT /api/v1/testcases/{uuid}
Content-Type: application/json

{
  "title": "新的测试用例标题",
  "priority": "P1",
  "description": "更新后的描述",
  "requires_self_test": true
}
```

### 提示词生成

#### 生成测试用例转换提示词
```http
POST /api/v1/prompts/testcase-conversion
Content-Type: application/json

{
  "document_content": "用户登录模块，测试手机号格式校验功能。优先级为P0，需开发人员自测。"
}
```

### 飞书文档

#### 获取飞书文档内容
```http
POST /api/v1/feishu/fetch-doc
Content-Type: application/json

{
  "doc_link": "https://example.feishu.cn/docx/abc123",
  "lang": 0
}
```

## 响应格式

所有API接口都返回统一的响应格式：

```json
{
  "status": "success",
  "message": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

**状态码说明：**
- `200`: 成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 使用示例

### Python示例

```python
import requests

# 创建测试用例集
response = requests.post('http://localhost:8001/api/v1/testcase-sets', json={
    "name": "API测试用例集",
    "prd_doc_link": "https://example.com/prd",
    "prd_doc_type": "feishu_doc",
    "prd_doc_format": "markdown",
    "author": "API用户"
})

if response.status_code == 200:
    result = response.json()
    if result["status"] == "success":
        set_uuid = result["data"]["uuid"]
        print(f"测试用例集创建成功，UUID: {set_uuid}")
        
        # 创建测试用例
        case_response = requests.post('http://localhost:8001/api/v1/testcases', json={
            "test_case_set_uuid": set_uuid,
            "title": "API接口测试",
            "priority": "P1",
            "description": "测试API接口功能"
        })
        
        if case_response.status_code == 200:
            case_result = case_response.json()
            print(f"测试用例创建成功: {case_result['data']['title']}")
```

### curl示例

```bash
# 创建测试用例集
curl -X POST "http://localhost:8001/api/v1/testcase-sets" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "curl测试用例集",
    "prd_doc_link": "https://example.com/prd",
    "author": "curl用户"
  }'

# 获取测试用例集列表
curl -X GET "http://localhost:8001/api/v1/testcase-sets?limit=10&offset=0"

# 健康检查
curl -X GET "http://localhost:8001/health"
```

## MCP协议接口

MCP协议接口挂载在 `/mcp` 路径下，提供以下工具函数：

- `create_testcase_set`: 创建测试用例集
- `query_testcase_set_info`: 查询测试用例集信息
- `list_all_testcase_sets`: 列出所有测试用例集
- `add_testcase_to_set`: 添加测试用例到用例集
- `list_testcases_in_set`: 列出用例集中的测试用例
- `modify_testcase_details`: 修改测试用例详情

以及提示词函数：
- `generate_testcase_conversion_prompt`: 生成测试用例转换提示词

## 注意事项

1. **数据库**: 服务使用MySQL数据库，确保数据库服务正常运行
2. **飞书配置**: 使用飞书文档功能需要配置 `feishu_config.toml` 文件
3. **端口**: 默认端口为8001，可在代码中修改
4. **CORS**: 服务已配置CORS支持跨域请求
5. **日志**: 所有请求都会记录日志，便于调试和监控

## 错误处理

API会返回详细的错误信息，包括：
- 参数验证错误
- 数据库操作错误
- 业务逻辑错误

请根据返回的错误信息进行相应的处理。
