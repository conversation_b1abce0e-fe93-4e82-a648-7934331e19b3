#!/usr/bin/env python3
"""
测试新增的PRD文档内容和更新日期字段
"""

from datetime import datetime
from mcp_tools.testcase_management import (
    create_testcase_set,
    query_testcase_set_info,
    list_all_testcase_sets
)

def test_new_fields():
    """测试新增字段功能"""
    print("🧪 测试新增的PRD文档字段...")
    
    # 测试数据
    test_prd_content = """
    # 用户登录功能PRD文档
    
    ## 功能概述
    用户可以通过手机号和密码进行登录
    
    ## 功能详情
    1. 手机号格式验证
       - 优先级：P0
       - 需要自测：是
       - 前置条件：用户进入登录页面
       - 操作步骤：输入不符合格式的手机号
    
    2. 密码强度验证
       - 优先级：P1
       - 需要自测：否
       - 前置条件：用户输入有效手机号
       - 操作步骤：输入弱密码
    """
    
    test_prd_updated_at = datetime(2025, 7, 30, 10, 30, 0)
    
    try:
        # 1. 创建包含新字段的测试用例集
        print("\n1. 创建包含PRD内容和更新日期的测试用例集...")
        result = create_testcase_set(
            name="用户登录功能测试用例集",
            prd_doc_link="https://example.feishu.cn/docx/login123",
            prd_doc_title="用户登录功能PRD文档",
            prd_doc_content=test_prd_content,
            prd_doc_updated_at=test_prd_updated_at,
            author="测试用户"
        )
        
        if result["status"] == "success":
            print(f"✅ 创建成功，UUID: {result['data']['uuid']}")
            test_case_set_uuid = result["data"]["uuid"]
            
            # 验证返回的数据包含新字段
            data = result["data"]
            if "prd_doc_content" in data and "prd_doc_updated_at" in data:
                print(f"✅ 新字段已包含在返回数据中")
                print(f"   PRD内容长度: {len(data['prd_doc_content']) if data['prd_doc_content'] else 0} 字符")
                print(f"   PRD更新日期: {data['prd_doc_updated_at']}")
            else:
                print("❌ 新字段未包含在返回数据中")
                return False
            
            # 2. 查询测试用例集信息，验证新字段
            print("\n2. 查询测试用例集信息...")
            query_result = query_testcase_set_info(test_case_set_uuid)
            
            if query_result["status"] == "success":
                query_data = query_result["data"]
                print(f"✅ 查询成功")
                
                # 验证PRD内容
                if query_data.get("prd_doc_content"):
                    print(f"✅ PRD文档内容已保存，长度: {len(query_data['prd_doc_content'])} 字符")
                    # 显示内容片段
                    content_preview = query_data['prd_doc_content'][:100] + "..." if len(query_data['prd_doc_content']) > 100 else query_data['prd_doc_content']
                    print(f"   内容预览: {content_preview}")
                else:
                    print("❌ PRD文档内容未保存")
                
                # 验证PRD更新日期
                if query_data.get("prd_doc_updated_at"):
                    print(f"✅ PRD文档更新日期已保存: {query_data['prd_doc_updated_at']}")
                else:
                    print("❌ PRD文档更新日期未保存")
            else:
                print(f"❌ 查询失败: {query_result['message']}")
                return False
            
            # 3. 列出所有测试用例集，验证新字段
            print("\n3. 列出所有测试用例集...")
            list_result = list_all_testcase_sets(limit=5)
            
            if list_result["status"] == "success":
                print(f"✅ 列表查询成功，找到 {list_result['count']} 个测试用例集")
                
                # 查找我们刚创建的测试用例集
                found_set = None
                for test_set in list_result["data"]:
                    if test_set["uuid"] == test_case_set_uuid:
                        found_set = test_set
                        break
                
                if found_set:
                    print("✅ 在列表中找到了新创建的测试用例集")
                    if found_set.get("prd_doc_content") and found_set.get("prd_doc_updated_at"):
                        print("✅ 列表数据中包含新字段")
                    else:
                        print("❌ 列表数据中缺少新字段")
                else:
                    print("❌ 在列表中未找到新创建的测试用例集")
            else:
                print(f"❌ 列表查询失败: {list_result['message']}")
                return False
            
            # 4. 测试不包含新字段的创建（向后兼容性）
            print("\n4. 测试向后兼容性（不包含新字段）...")
            compat_result = create_testcase_set(
                name="兼容性测试用例集",
                prd_doc_link="https://example.feishu.cn/docx/compat123",
                author="兼容性测试"
            )
            
            if compat_result["status"] == "success":
                print("✅ 向后兼容性测试通过")
                compat_data = compat_result["data"]
                if compat_data.get("prd_doc_content") is None and compat_data.get("prd_doc_updated_at") is None:
                    print("✅ 新字段正确设置为 None")
                else:
                    print("❌ 新字段未正确处理空值")
            else:
                print(f"❌ 向后兼容性测试失败: {compat_result['message']}")
                return False
            
            return True
            
        else:
            print(f"❌ 创建失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试新增的PRD文档字段...")
    
    success = test_new_fields()
    
    if success:
        print("\n🎉 所有测试通过！新字段功能正常！")
        print("\n📋 新增字段说明:")
        print("  - prd_doc_content: 存储PRD文档的完整文本内容")
        print("  - prd_doc_updated_at: 存储PRD文档的更新日期")
        print("  - 两个字段都是可选的，支持向后兼容")
    else:
        print("\n❌ 测试失败，请检查代码")

if __name__ == "__main__":
    main()
