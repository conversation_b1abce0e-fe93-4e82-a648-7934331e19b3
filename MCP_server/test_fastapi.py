#!/usr/bin/env python3
"""
测试FastAPI接口
"""

import requests
import json
from datetime import datetime

# 服务器配置
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api/v1"

def test_health_check():
    """测试健康检查接口"""
    print("🧪 测试健康检查接口...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_api_info():
    """测试API信息接口"""
    print("\n🧪 测试API信息接口...")
    try:
        response = requests.get(f"{BASE_URL}/api/info")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API信息获取成功:")
            print(f"   名称: {data.get('name')}")
            print(f"   版本: {data.get('version')}")
            print(f"   描述: {data.get('description')}")
            return True
        else:
            print(f"❌ API信息获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API信息获取异常: {e}")
        return False

def test_create_testcase_set():
    """测试创建测试用例集接口"""
    print("\n🧪 测试创建测试用例集接口...")
    try:
        data = {
            "name": "FastAPI测试用例集",
            "prd_doc_link": "https://example.feishu.cn/docx/fastapi123",
            "prd_doc_title": "FastAPI测试PRD文档",
            "prd_doc_content": "这是通过FastAPI接口创建的测试用例集",
            "prd_doc_type": "feishu_doc",
            "prd_doc_format": "markdown",
            "author": "FastAPI测试用户"
        }
        
        response = requests.post(f"{API_BASE}/testcase-sets", json=data)
        if response.status_code == 200:
            result = response.json()
            if result["status"] == "success":
                print(f"✅ 测试用例集创建成功:")
                print(f"   UUID: {result['data']['uuid']}")
                print(f"   名称: {result['data']['name']}")
                print(f"   类型: {result['data']['prd_doc_type']}")
                print(f"   格式: {result['data']['prd_doc_format']}")
                return result['data']['uuid']
            else:
                print(f"❌ 测试用例集创建失败: {result['message']}")
                return None
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 创建测试用例集异常: {e}")
        return None

def test_get_testcase_set(uuid):
    """测试获取测试用例集接口"""
    print(f"\n🧪 测试获取测试用例集接口 (UUID: {uuid})...")
    try:
        response = requests.get(f"{API_BASE}/testcase-sets/{uuid}")
        if response.status_code == 200:
            result = response.json()
            if result["status"] == "success":
                print(f"✅ 测试用例集获取成功:")
                print(f"   名称: {result['data']['name']}")
                print(f"   作者: {result['data']['author']}")
                print(f"   创建时间: {result['data']['created_at']}")
                return True
            else:
                print(f"❌ 测试用例集获取失败: {result['message']}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取测试用例集异常: {e}")
        return False

def test_list_testcase_sets():
    """测试获取测试用例集列表接口"""
    print("\n🧪 测试获取测试用例集列表接口...")
    try:
        response = requests.get(f"{API_BASE}/testcase-sets?limit=5&offset=0")
        if response.status_code == 200:
            result = response.json()
            if result["status"] == "success":
                print(f"✅ 测试用例集列表获取成功:")
                print(f"   总数: {result['count']}")
                for i, item in enumerate(result['data'][:3]):  # 只显示前3个
                    print(f"   [{i+1}] {item['name']} (UUID: {item['uuid']})")
                return True
            else:
                print(f"❌ 测试用例集列表获取失败: {result['message']}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取测试用例集列表异常: {e}")
        return False

def test_create_testcase(set_uuid):
    """测试创建测试用例接口"""
    print(f"\n🧪 测试创建测试用例接口 (集合UUID: {set_uuid})...")
    try:
        data = {
            "test_case_set_uuid": set_uuid,
            "title": "FastAPI接口测试用例",
            "priority": "P1",
            "description": "通过FastAPI接口创建的测试用例"
        }
        
        response = requests.post(f"{API_BASE}/testcases", json=data)
        if response.status_code == 200:
            result = response.json()
            if result["status"] == "success":
                print(f"✅ 测试用例创建成功:")
                print(f"   UUID: {result['data']['uuid']}")
                print(f"   标题: {result['data']['title']}")
                print(f"   优先级: {result['data']['priority']}")
                return result['data']['uuid']
            else:
                print(f"❌ 测试用例创建失败: {result['message']}")
                return None
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 创建测试用例异常: {e}")
        return None

def test_list_testcases(set_uuid):
    """测试获取测试用例列表接口"""
    print(f"\n🧪 测试获取测试用例列表接口 (集合UUID: {set_uuid})...")
    try:
        response = requests.get(f"{API_BASE}/testcase-sets/{set_uuid}/testcases")
        if response.status_code == 200:
            result = response.json()
            if result["status"] == "success":
                print(f"✅ 测试用例列表获取成功:")
                print(f"   总数: {result['count']}")
                for i, item in enumerate(result['data']):
                    print(f"   [{i+1}] {item['title']} (优先级: {item['priority']})")
                return True
            else:
                print(f"❌ 测试用例列表获取失败: {result['message']}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取测试用例列表异常: {e}")
        return False

def test_generate_prompt():
    """测试生成提示词接口"""
    print("\n🧪 测试生成提示词接口...")
    try:
        data = {
            "document_content": "用户登录模块，测试手机号格式校验功能。优先级为P0，需开发人员自测。"
        }
        
        response = requests.post(f"{API_BASE}/prompts/testcase-conversion", json=data)
        if response.status_code == 200:
            result = response.json()
            if result["status"] == "success":
                prompt = result['data']['prompt']
                print(f"✅ 提示词生成成功:")
                print(f"   长度: {len(prompt)} 字符")
                print(f"   预览: {prompt[:100]}...")
                return True
            else:
                print(f"❌ 提示词生成失败: {result['message']}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 生成提示词异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试FastAPI接口...")
    
    results = []
    
    # 基础接口测试
    results.append(test_health_check())
    results.append(test_api_info())
    
    # 测试用例集相关接口
    set_uuid = test_create_testcase_set()
    if set_uuid:
        results.append(True)
        results.append(test_get_testcase_set(set_uuid))
        results.append(test_list_testcase_sets())
        
        # 测试用例相关接口
        case_uuid = test_create_testcase(set_uuid)
        if case_uuid:
            results.append(True)
            results.append(test_list_testcases(set_uuid))
        else:
            results.append(False)
            results.append(False)
    else:
        results.extend([False, False, False, False, False])
    
    # 提示词接口测试
    results.append(test_generate_prompt())
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 测试结果统计:")
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 所有FastAPI接口测试通过！")
        print("\n📋 可用的API接口:")
        print("  - GET  /health                              # 健康检查")
        print("  - GET  /api/info                            # API信息")
        print("  - POST /api/v1/testcase-sets                # 创建测试用例集")
        print("  - GET  /api/v1/testcase-sets/{uuid}         # 获取测试用例集")
        print("  - GET  /api/v1/testcase-sets                # 获取测试用例集列表")
        print("  - POST /api/v1/testcases                    # 创建测试用例")
        print("  - GET  /api/v1/testcase-sets/{uuid}/testcases # 获取测试用例列表")
        print("  - PUT  /api/v1/testcases/{uuid}             # 更新测试用例")
        print("  - POST /api/v1/prompts/testcase-conversion  # 生成提示词")
        print("  - POST /api/v1/feishu/fetch-doc             # 获取飞书文档")
        print("  - GET  /docs                                # API文档")
    else:
        print(f"\n❌ 有 {total - passed} 个接口测试失败")
        print("请确保服务器正在运行: python3 fastmcp_server.py")

if __name__ == "__main__":
    main()
