"""
安装相关的库：python3 -m pip install fastmcp
MCP有三种通信协议：sse、stdio、streamable-http
"""
# server.py
from fastmcp import FastMCP
from fastmcp.server.context import Context
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.routing import Mount
from starlette.applications import Starlette
from mcp_tools.feishu_doc import get_app_access_token, get_doc_raw_content, fetch_feishu_doc_content
from utils.database import JSONStorage
from mcp_tools.tool_registry import register_all_tools
from api.fastapi_routes import create_fastapi_app
import pandas as pd
import io
import json
import re

mcp = FastMCP("MCP server")

class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        body = await request.body()
        print(f"[HTTP] {request.method} {request.url.path}?{request.url.query}")
        print(f"[HTTP] Headers: {dict(request.headers)}")
        print(f"[HTTP] Client: {request.client.host}:{request.client.port}")
        print(f"[HTTP] Raw Body: {body!r}")
        try:
            import json
            parsed = json.loads(body)
            print(f"[HTTP] Parsed JSON: {parsed}")
        except Exception as e:
            print(f"[HTTP] JSON parse error: {e}")
        try:
            response = await call_next(request)
        except Exception as e:
            print(f"[HTTP] Exception during request handling: {e}")
            raise
        print(f"[HTTP] Response status: {response.status_code}")
        return response

# 创建集成应用
def create_integrated_app():
    """创建集成MCP和FastAPI的应用"""
    # 创建MCP应用
    mcp_app = mcp.streamable_http_app()
    mcp_app.add_middleware(LoggingMiddleware)

    # 创建FastAPI应用
    fastapi_app = create_fastapi_app()

    # 创建主Starlette应用，集成两个子应用
    from starlette.applications import Starlette
    from starlette.routing import Mount

    main_app = Starlette(
        routes=[
            Mount("/mcp", app=mcp_app),  # MCP服务挂载到 /mcp 路径
            Mount("/", app=fastapi_app),  # FastAPI挂载到根路径
        ]
    )

    return main_app

# 保持向后兼容的函数名
def sse_app_with_logging():
    """保持向后兼容"""
    return create_integrated_app()


@mcp.tool()
def add(a: float, b: float, ctx: Context = None) -> float:
    if ctx:
        print(f"[add] request_id={ctx.request_id}, client_id={ctx.client_id}")
    print(f"[add] Received parameters: a={a}, b={b}")
    c = a + b
    print(f"[add] Result: {c}")
    return c


@mcp.tool()
def multiply(a: float, b: float, ctx: Context = None) -> float:
    if ctx:
        print(f"[multiply] request_id={ctx.request_id}, client_id={ctx.client_id}")
    print(f"[multiply] Received parameters: a={a}, b={b}")
    result = a * b
    print(f"[multiply] Result: {result}")
    return result


@mcp.tool()
def subtract(a: float, b: float, ctx: Context = None) -> float:
    if ctx:
        print(f"[subtract] request_id={ctx.request_id}, client_id={ctx.client_id}")
    print(f"[subtract] Received parameters: a={a}, b={b}")
    result = a - b
    print(f"[subtract] Result: {result}")
    return result

@mcp.tool()
def divide(a: float, b: float, ctx: Context = None) -> float:
    if ctx:
        print(f"[divide] request_id={ctx.request_id}, client_id={ctx.client_id}")
    print(f"[divide] Received parameters: a={a}, b={b}")
    if b == 0:
        print("[divide] Error: Division by zero")
        return float('inf')
    result = a / b
    print(f"[divide] Result: {result}")
    return result


@mcp.tool()
def fetch_feishu_doc(doc_link: str, lang: int = 0, ctx: Context = None) -> dict:
    """
    获取飞书文档纯文本内容。
    参数:
        doc_link: 飞书文档链接
        lang: 语言参数，0为中文，1为英文（可选）
    返回:
        文档内容或错误信息
    """
    return fetch_feishu_doc_content(doc_link, lang)


@mcp.tool()
def markdown_table_to_json(markdown_content: str, ctx: Context = None) -> dict:
    """
    将markdown表格转换为JSON格式。
    参数:
        markdown_content: 包含markdown表格的字符串内容
    返回:
        包含转换结果的字典，包括JSON数据和状态信息
    """
    if ctx:
        print(f"[markdown_table_to_json] request_id={ctx.request_id}, client_id={ctx.client_id}")
    
    print(f"[markdown_table_to_json] Received markdown content length: {len(markdown_content)}")
    
    try:
        # 清理markdown内容，移除可能的markdown语法
        lines = markdown_content.strip().split('\n')
        table_lines = []
        
        for line in lines:
            line = line.strip()
            # 跳过空行和分隔符行（如 |---|---| 或 |:---|:---|）
            if line and not re.match(r'^\|[\s\-:|\|]+\|$', line):
                table_lines.append(line)
        
        if not table_lines:
            raise ValueError("未找到有效的表格内容")
        
        # 重新组合表格内容，并清理每行开头和结尾的 |
        cleaned_lines = []
        for line in table_lines:
            # 移除开头和结尾的 | 符号
            cleaned_line = line.strip()
            if cleaned_line.startswith('|'):
                cleaned_line = cleaned_line[1:]
            if cleaned_line.endswith('|'):
                cleaned_line = cleaned_line[:-1]
            cleaned_lines.append(cleaned_line)
        
        table_content = '\n'.join(cleaned_lines)
        
        # 使用pandas读取markdown表格
        table_data = pd.read_table(io.StringIO(table_content), sep='|')
        
        # 清理列名（去除空格和特殊字符）
        table_data.columns = table_data.columns.str.strip()
        
        # 清理数据（去除首尾空格）
        for col in table_data.columns:
            if table_data[col].dtype == 'object':
                table_data[col] = table_data[col].str.strip()
        
        # 转换为JSON格式
        json_data = table_data.to_dict('records')
        
        result = {
            "status": "success",
            "message": "Markdown表格成功转换为JSON",
            "data": json_data,
            "row_count": len(json_data),
            "column_count": len(table_data.columns) if len(json_data) > 0 else 0,
            "columns": list(table_data.columns)
        }
        
        print(f"[markdown_table_to_json] Successfully converted table with {len(json_data)} rows")
        return result
        
    except Exception as e:
        error_result = {
            "status": "error",
            "message": f"转换失败: {str(e)}",
            "data": None,
            "row_count": 0,
            "column_count": 0,
            "columns": []
        }
        print(f"[markdown_table_to_json] Error: {str(e)}")
        return error_result


@mcp.tool()
def json_to_markdown_table(json_data: str, ctx: Context = None) -> dict:
    """
    将JSON数据转换为markdown表格格式。
    参数:
        json_data: JSON字符串，可以是数组格式的JSON数据
    返回:
        包含转换结果的字典，包括markdown表格和状态信息
    """
    if ctx:
        print(f"[json_to_markdown_table] request_id={ctx.request_id}, client_id={ctx.client_id}")
    
    print(f"[json_to_markdown_table] Received JSON data length: {len(json_data)}")
    
    try:
        # 解析JSON数据
        data = json.loads(json_data)
        
        # 确保数据是列表格式
        if not isinstance(data, list):
            data = [data]
        
        if not data:
            raise ValueError("JSON数据为空")
        
        # 转换为pandas DataFrame
        df = pd.DataFrame(data)
        
        # 生成markdown表格
        markdown_table = df.to_markdown(index=False)
        
        result = {
            "status": "success",
            "message": "JSON数据成功转换为Markdown表格",
            "markdown_table": markdown_table,
            "row_count": len(df),
            "column_count": len(df.columns),
            "columns": list(df.columns)
        }
        
        print(f"[json_to_markdown_table] Successfully converted JSON with {len(df)} rows to markdown")
        return result
        
    except json.JSONDecodeError as e:
        error_result = {
            "status": "error",
            "message": f"JSON解析失败: {str(e)}",
            "markdown_table": None,
            "row_count": 0,
            "column_count": 0,
            "columns": []
        }
        print(f"[json_to_markdown_table] JSON decode error: {str(e)}")
        return error_result
        
    except Exception as e:
        error_result = {
            "status": "error",
            "message": f"转换失败: {str(e)}",
            "markdown_table": None,
            "row_count": 0,
            "column_count": 0,
            "columns": []
        }
        print(f"[json_to_markdown_table] Error: {str(e)}")
        return error_result


# 注册所有MCP工具函数
register_all_tools(mcp)


# 主程序入口
if __name__ == "__main__":
    import uvicorn
    app = sse_app_with_logging()
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
